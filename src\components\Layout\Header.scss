@use '../../styles/variables';

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: $header-height;
  background: $bg-primary;
  border-bottom: 1px solid $border-color;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $spacing-md;
  z-index: $z-header;
  box-shadow: 0 2px 8px $shadow-light;

  &__left {
    display: flex;
    align-items: center;
    gap: $spacing-md;
  }

  &__menu-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: $spacing-sm;
    border-radius: $border-radius-sm;
    color: $text-primary;
    transition: background-color $transition-fast;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: $bg-secondary;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__title {
    font-size: 1.25rem;
    font-weight: 600;
    color: $text-primary;
    margin: 0;

    // Hide on very small screens
    @media (max-width: 480px) {
      display: none;
    }
  }

  &__right {
    display: flex;
    align-items: center;
  }

  &__user {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }

  &__user-info {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    // Hide username on small screens
    @media (max-width: $breakpoint-sm) {
      .header__username {
        display: none;
      }
    }
  }

  &__username {
    font-weight: 500;
    color: $text-primary;
    font-size: 0.875rem;
  }

  &__status {
    font-size: 0.75rem;
    color: $text-secondary;
    padding: 2px 6px;
    border-radius: $border-radius-sm;
    background-color: $bg-secondary;

    // Hide on very small screens
    @media (max-width: 360px) {
      display: none;
    }
  }

  &__logout-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: $spacing-sm;
    border-radius: $border-radius-sm;
    color: $text-secondary;
    transition: all $transition-fast;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: $bg-secondary;
      color: $error-color;
    }
  }

  &__auth {
    font-size: 0.875rem;
    color: $text-secondary;
  }

  // Tablet and up
  @media (min-width: $breakpoint-md) {
    padding: 0 $spacing-lg;

    &__menu-button {
      display: none;
    }
  }

  // Desktop and up
  @media (min-width: $breakpoint-lg) {
    padding: 0 $spacing-xl;
  }
}