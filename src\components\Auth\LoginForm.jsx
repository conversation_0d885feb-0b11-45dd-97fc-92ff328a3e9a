import React, { useState } from 'react';
import { User, Lock, Eye, EyeOff, LogIn } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import ErrorMessage from '@/components/UI/ErrorMessage';
import './LoginForm.scss';
import LoadingSpinner from '@/components/UI/LoadingSpinner';

const LoginForm = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Development phase - simple credential check
      if (username === 'admin' && password === '123admin') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock user data and token for development
        const mockUser = {
          id: '1',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'administrator',
          isAuthenticated: true,
          avatar: null
        };

        const mockToken = 'dev_token_' + Date.now();

        login(mockToken, mockUser);
      } else {
        throw new Error('Invalid credentials. Use username: admin, password: 123admin');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setUsername('admin');
    setPassword('123admin');
  };

  return (
    <div className="login-form">
      <div className="login-form__container">
        <div className="login-form__header">
          <div className="login-form__logo">
            <h1>Zalo Dashboard</h1>
            <p>Development Environment</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="login-form__form">
          {error && (
            <ErrorMessage
              message={error}
              onRetry={() => setError('')}
              showIcon={false}
            />
          )}

          <div className="login-form__field">
            <label htmlFor="username" className="login-form__label">
              Username
            </label>
            <div className="login-form__input-wrapper">
              <User className="login-form__input-icon" size={20} />
              <input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="login-form__input"
                placeholder="Enter username"
                required
                autoComplete="username"
              />
            </div>
          </div>

          <div className="login-form__field">
            <label htmlFor="password" className="login-form__label">
              Password
            </label>
            <div className="login-form__input-wrapper">
              <Lock className="login-form__input-icon" size={20} />
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="login-form__input"
                placeholder="Enter password"
                required
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="login-form__toggle-password"
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading || !username || !password}
            className="login-form__submit"
          >
            {isLoading ? (
              <LoadingSpinner size="small" text="" />
            ) : (
              <>
                <LogIn size={20} />
                Sign In
              </>
            )}
          </button>

          <div className="login-form__demo">
            <p className="login-form__demo-text">Development Credentials:</p>
            <button
              type="button"
              onClick={handleDemoLogin}
              className="login-form__demo-button"
            >
              Use Demo Login (admin / 123admin)
            </button>
          </div>
        </form>

        <div className="login-form__footer">
          <p>Development Environment - For testing purposes only</p>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;