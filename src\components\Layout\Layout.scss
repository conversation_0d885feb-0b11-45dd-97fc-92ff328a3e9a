@use '../../styles/variables' as *;

.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  &__body {
    display: flex;
    flex: 1;
    margin-top: $header-height;

    @media (min-width: $breakpoint-md) {
      margin-top: $header-height;
    }
  }

  &__main {
    flex: 1;
    overflow-x: auto;

    // On mobile, full width
    width: 100%;

    // On tablet and up, account for sidebar
    @media (min-width: $breakpoint-md) {
      margin-left: $sidebar-width;
      width: calc(100% - #{$sidebar-width});
    }

    // Large desktop
    @media (min-width: $breakpoint-xl) {
      margin-left: 280px;
      width: calc(100% - 280px);
    }
  }
}

// Global styles for dashboard
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: $bg-secondary;
  color: $text-primary;
}

// Focus styles for accessibility
button:focus,
a:focus {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}

// Smooth scrolling
html {
  scroll-behavior: smooth;
}

