@use '../../styles/variables' as *;

.error-message {
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  padding: $spacing-lg;
  border-radius: $border-radius-md;
  margin: $spacing-md 0;

  &--error {
    background-color: rgba(255, 77, 79, 0.1);
    border: 1px solid rgba(255, 77, 79, 0.3);
    color: $error-color;

    .error-message__icon {
      color: $error-color;
    }
  }

  &--warning {
    background-color: rgba(250, 173, 20, 0.1);
    border: 1px solid rgba(250, 173, 20, 0.3);
    color: $warning-color;

    .error-message__icon {
      color: $warning-color;
    }
  }

  &__icon {
    flex-shrink: 0;
    margin-top: 2px;
  }

  &__content {
    flex: 1;
  }

  &__text {
    margin: 0 0 $spacing-sm 0;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  &__retry-button {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background: none;
    border: 1px solid currentColor;
    color: inherit;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-sm;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all $transition-fast;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // Mobile responsive
  @media (max-width: $breakpoint-sm) {
    padding: $spacing-md;
    gap: $spacing-sm;

    &__text {
      font-size: 0.8rem;
    }

    &__retry-button {
      padding: $spacing-xs $spacing-sm;
      font-size: 0.7rem;
    }
  }
}

