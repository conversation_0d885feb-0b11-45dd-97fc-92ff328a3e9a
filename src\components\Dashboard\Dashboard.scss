@use '../../styles/variables' as *;

.dashboard {
  &__metrics {
    display: grid;
    gap: $spacing-md;
    margin-bottom: $spacing-xl;

    // Mobile: 1 column
    grid-template-columns: 1fr;

    // Tablet: 2 columns
    @media (min-width: $breakpoint-sm) {
      grid-template-columns: repeat(2, 1fr);
    }

    // Desktop: 4 columns
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__content {
    display: grid;
    gap: $spacing-lg;
    grid-template-columns: 1fr;

    // Desktop: Two column layout
    @media (min-width: $breakpoint-lg) {
      grid-template-columns: 1fr 1fr;
    }
  }

  &__activity {
    &-widget {
      width: 100%;
    }
  }

  &__zalo {
    width: 100%;
  }
}

.metric-card {
  display: flex;
  align-items: center;
  gap: $spacing-md;

  &__icon {
    color: $primary-color;
    flex-shrink: 0;

    &--success {
      color: $success-color;
    }

    &--info {
      color: #1890ff;
    }

    &--warning {
      color: $warning-color;
    }
  }

  &__content {
    flex: 1;
  }

  &__value {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-primary;
    line-height: 1.2;

    @media (min-width: $breakpoint-md) {
      font-size: 1.75rem;
    }
  }

  &__label {
    font-size: 0.875rem;
    color: $text-secondary;
    margin-top: $spacing-xs;
  }

  // Stack vertically on very small screens
  @media (max-width: 360px) {
    flex-direction: column;
    text-align: center;
    gap: $spacing-sm;

    &__icon {
      align-self: center;
    }
  }
}

.activity-list {
  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    color: $text-secondary;
    text-align: center;

    svg {
      margin-bottom: $spacing-md;
      opacity: 0.5;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }
}

.activity-item {
  display: flex;
  gap: $spacing-md;
  padding: $spacing-md 0;
  border-bottom: 1px solid $border-color;

  &:last-child {
    border-bottom: none;
  }

  &__avatar {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: $bg-secondary;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__avatar-placeholder {
    font-weight: 600;
    color: $text-secondary;
    font-size: 0.875rem;
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__message {
    font-size: 0.875rem;
    color: $text-primary;
    line-height: 1.5;
    margin-bottom: $spacing-xs;
  }

  &__meta {
    display: flex;
    gap: $spacing-sm;
    font-size: 0.75rem;
    color: $text-secondary;

    // Stack on small screens
    @media (max-width: $breakpoint-sm) {
      flex-direction: column;
      gap: 2px;
    }
  }

  &__user {
    font-weight: 500;
  }

  &__time {
    &::before {
      content: '•';
      margin-right: $spacing-xs;

      @media (max-width: $breakpoint-sm) {
        display: none;
      }
    }
  }

  // Improved touch targets for mobile
  @media (max-width: $breakpoint-md) {
    padding: $spacing-lg 0;

    &__avatar {
      width: 44px;
      height: 44px;
    }
  }
}

