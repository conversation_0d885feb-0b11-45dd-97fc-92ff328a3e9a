import React from 'react';
import { Menu, User, LogOut } from 'lucide-react';
import './Header.scss';

const Header = ({ onToggleSidebar, user, onLogout }) => {
  return (
    <header className="header">
      <div className="header__left">
        <button
          className="header__menu-button"
          onClick={onToggleSidebar}
          aria-label="Toggle sidebar"
        >
          <Menu size={24} />
        </button>
        <h1 className="header__title">Zalo Dashboard</h1>
      </div>

      <div className="header__right">
        {user ? (
          <div className="header__user">
            <div className="header__user-info">
              <User size={20} />
              <span className="header__username">{user.name}</span>
              <span className="header__status">
                {user.isAuthenticated ? 'Online' : 'Offline'}
              </span>
            </div>
            <button
              className="header__logout-button"
              onClick={onLogout}
              aria-label="Logout"
            >
              <LogOut size={20} />
            </button>
          </div>
        ) : (
          <div className="header__auth">
            <span className="header__status">Not logged in</span>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;