import React from 'react';
import { Apollo<PERSON><PERSON>ider } from '@apollo/client';
import { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './hooks/useAuth';
import Layout from './components/Layout/Layout';
import Dashboard from './components/Dashboard/Dashboard';
import LoginForm from './components/Auth/LoginForm';
import LoadingSpinner from './components/UI/LoadingSpinner';
import ErrorMessage from './components/UI/ErrorMessage';
import client from './graphql/client';
import './App.css';

const AppContent = () => {
  const { user, loading, error, logout, isAuthenticated } = useAuth();

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: '#f5f5f5'
      }}>
        <LoadingSpinner size="large" text="Loading application..." />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: '#f5f5f5',
        padding: '20px'
      }}>
        <ErrorMessage
          message={`Authentication error: ${error}`}
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return (
    <Layout user={user} onLogout={logout}>
      <Dashboard />
    </Layout>
  );
};

function App() {
  return (
    <ApolloProvider client={client}>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ApolloProvider>
  );
}

export default App;
