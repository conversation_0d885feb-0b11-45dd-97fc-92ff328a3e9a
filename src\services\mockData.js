// Mock data for development phase
export const mockDashboardData = {
  metrics: {
    totalUsers: 15423,
    activeUsers: 8967,
    totalMessages: 245683,
    growthRate: 12.5
  },
  recentActivity: [
    {
      id: '1',
      type: 'user_joined',
      message: 'New user registered',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
      user: {
        name: '<PERSON><PERSON><PERSON>',
        avatar: null
      }
    },
    {
      id: '2',
      type: 'message_sent',
      message: 'Message sent to group chat',
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
      user: {
        name: '<PERSON><PERSON> <PERSON><PERSON>',
        avatar: null
      }
    },
    {
      id: '3',
      type: 'user_online',
      message: 'User came online',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      user: {
        name: '<PERSON>',
        avatar: null
      }
    },
    {
      id: '4',
      type: 'message_sent',
      message: 'Broadcast message sent',
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
      user: {
        name: '<PERSON><PERSON>',
        avatar: null
      }
    },
    {
      id: '5',
      type: 'user_joined',
      message: 'New user registered',
      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1 hour ago
      user: {
        name: 'Hoang Van E',
        avatar: null
      }
    }
  ],
  analytics: {
    userGrowth: [
      { date: '2024-01-01', count: 1000 },
      { date: '2024-01-02', count: 1150 },
      { date: '2024-01-03', count: 1200 },
      { date: '2024-01-04', count: 1350 },
      { date: '2024-01-05', count: 1500 },
      { date: '2024-01-06', count: 1650 },
      { date: '2024-01-07', count: 1800 }
    ],
    messageVolume: [
      { date: '2024-01-01', count: 5000 },
      { date: '2024-01-02', count: 5500 },
      { date: '2024-01-03', count: 6000 },
      { date: '2024-01-04', count: 6200 },
      { date: '2024-01-05', count: 6800 },
      { date: '2024-01-06', count: 7200 },
      { date: '2024-01-07', count: 7500 }
    ]
  }
};

export const mockUsers = [
  {
    id: '1',
    name: 'Nguyen Van Admin',
    email: '<EMAIL>',
    status: 'online',
    lastActive: new Date().toISOString(),
    avatar: null
  },
  {
    id: '2',
    name: 'Tran Thi User',
    email: '<EMAIL>',
    status: 'offline',
    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    avatar: null
  }
];

// Simulate API delay
export const delay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms));