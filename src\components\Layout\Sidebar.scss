@use '../../styles/variables' as *;

.sidebar {
  position: fixed;
  top: $header-height;
  left: 0;
  width: $sidebar-width;
  height: calc(100vh - #{$header-height});
  background: $bg-dark;
  color: $text-light;
  transform: translateX(-100%);
  transition: transform $transition-normal;
  z-index: $z-sidebar;
  display: flex;
  flex-direction: column;

  &--open {
    transform: translateX(0);
  }

  &__overlay {
    position: fixed;
    top: $header-height;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: $z-overlay;

    @media (min-width: $breakpoint-md) {
      display: none;
    }
  }

  &__header {
    padding: $spacing-lg $spacing-md;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-light;
    margin: 0;
  }

  &__close-button {
    background: none;
    border: none;
    color: $text-light;
    cursor: pointer;
    padding: $spacing-sm;
    border-radius: $border-radius-sm;
    transition: background-color $transition-fast;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    @media (min-width: $breakpoint-md) {
      display: none;
    }
  }

  &__nav {
    flex: 1;
    padding: $spacing-md 0;
  }

  &__menu {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__menu-item {
    margin-bottom: $spacing-xs;
  }

  &__menu-link {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    padding: $spacing-md;
    margin: 0 $spacing-md;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    border-radius: $border-radius-md;
    transition: all $transition-fast;
    cursor: pointer;
    width: calc(100% - #{$spacing-md * 2});
    text-align: left;
    font-size: 0.875rem;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: $text-light;
    }

    &--active {
      background-color: $primary-color;
      color: $text-light;

      &:hover {
        background-color: $primary-hover;
      }
    }
  }

  &__menu-text {
    font-weight: 500;
  }

  &__footer {
    padding: $spacing-md;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
  }

  &__version {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.5);
  }

  // Tablet and up - sidebar visible by default
  @media (min-width: $breakpoint-md) {
    position: static;
    height: calc(100vh - #{$header-height});
    transform: translateX(0);
    width: $sidebar-width;

    &__overlay {
      display: none;
    }
  }

  // Large desktop - wider sidebar
  @media (min-width: $breakpoint-xl) {
    width: 280px;
  }
}

// Touch targets for mobile
@media (max-width: $breakpoint-md) {
  .sidebar {
    &__menu-link {
      padding: $spacing-lg $spacing-md;
      min-height: 48px;
    }

    &__close-button {
      min-width: 44px;
      min-height: 44px;
    }
  }
}