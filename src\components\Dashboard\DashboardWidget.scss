@use '../../styles/variables' as *;

.dashboard-widget {
  background: $bg-primary;
  border-radius: $border-radius-lg;
  border: 1px solid $border-color;
  box-shadow: 0 2px 8px $shadow-light;
  overflow: hidden;
  transition: box-shadow $transition-fast;

  &:hover {
    box-shadow: 0 4px 12px $shadow-medium;
  }

  &__header {
    padding: $spacing-lg $spacing-lg $spacing-md $spacing-lg;
    border-bottom: 1px solid $border-color;
    background: $bg-secondary;
  }

  &__title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: $text-primary;

    @media (min-width: $breakpoint-md) {
      font-size: 1.125rem;
    }
  }

  &__content {
    padding: $spacing-lg;
    min-height: 120px;

    @media (min-width: $breakpoint-md) {
      padding: $spacing-xl;
      min-height: 150px;
    }
  }

  // Widget variants
  &--small {
    .dashboard-widget__content {
      min-height: 80px;
      padding: $spacing-md;
    }
  }

  &--large {
    .dashboard-widget__content {
      min-height: 200px;

      @media (min-width: $breakpoint-md) {
        min-height: 300px;
      }
    }
  }

  // Full width on mobile, grid on larger screens
  @media (max-width: $breakpoint-md) {
    margin-bottom: $spacing-md;
  }
}

