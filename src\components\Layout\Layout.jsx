import React, { useState } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import MainContent from './MainContent';
import './Layout.scss';

const Layout = ({ children, user, onLogout }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeItem, setActiveItem] = useState('dashboard');

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleCloseSidebar = () => {
    setSidebarOpen(false);
  };

  const handleNavigate = (itemId) => {
    setActiveItem(itemId);
    setSidebarOpen(false);
  };

  return (
    <div className="layout">
      <Header
        onToggleSidebar={handleToggleSidebar}
        user={user}
        onLogout={onLogout}
      />
      <div className="layout__body">
        <Sidebar
          isOpen={sidebarOpen}
          onClose={handleCloseSidebar}
          activeItem={activeItem}
          onNavigate={handleNavigate}
        />
        <div className="layout__main">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Layout;