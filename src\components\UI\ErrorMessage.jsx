import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import './ErrorMessage.scss';

const ErrorMessage = ({
  message = 'Something went wrong',
  onRetry,
  variant = 'error',
  showIcon = true
}) => {
  return (
    <div className={`error-message error-message--${variant}`}>
      {showIcon && (
        <AlertCircle className="error-message__icon" size={24} />
      )}
      <div className="error-message__content">
        <p className="error-message__text">{message}</p>
        {onRetry && (
          <button
            className="error-message__retry-button"
            onClick={onRetry}
          >
            <RefreshCw size={16} />
            Try Again
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;