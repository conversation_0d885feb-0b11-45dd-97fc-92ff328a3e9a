{"name": "svk-zalo-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^4.0.5", "graphql": "^16.11.0", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.9.1", "sass-embedded": "^1.93.0"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react-swc": "^4.0.1", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "vite": "^7.1.6"}}