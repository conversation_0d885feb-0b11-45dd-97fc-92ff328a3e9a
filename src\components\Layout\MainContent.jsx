import React from 'react';
import './MainContent.scss';

const MainContent = ({ children, title, subtitle }) => {
  return (
    <main className="main-content">
      <div className="main-content__container">
        {(title || subtitle) && (
          <div className="main-content__header">
            {title && <h1 className="main-content__title">{title}</h1>}
            {subtitle && <p className="main-content__subtitle">{subtitle}</p>}
          </div>
        )}
        <div className="main-content__body">
          {children}
        </div>
      </div>
    </main>
  );
};

export default MainContent;