@use '@/styles/variables' as *;

.login-form {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: $spacing-md;

  &__container {
    background: $bg-primary;
    border-radius: $border-radius-lg;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    padding: $spacing-xxl;
    width: 100%;
    max-width: 400px;

    @media (max-width: $breakpoint-sm) {
      padding: $spacing-xl $spacing-lg;
      max-width: 350px;
    }
  }

  &__header {
    text-align: center;
    margin-bottom: $spacing-xl;
  }

  &__logo {
    h1 {
      margin: 0 0 $spacing-xs 0;
      font-size: 1.75rem;
      font-weight: 700;
      color: $text-primary;

      @media (max-width: $breakpoint-sm) {
        font-size: 1.5rem;
      }
    }

    p {
      margin: 0;
      font-size: 0.875rem;
      color: $text-secondary;
    }
  }

  &__form {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  &__field {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
  }

  &__label {
    font-size: 0.875rem;
    font-weight: 600;
    color: $text-primary;
  }

  &__input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  &__input-icon {
    position: absolute;
    left: $spacing-md;
    color: $text-secondary;
    z-index: 1;
  }

  &__input {
    width: 100%;
    padding: $spacing-md $spacing-md $spacing-md calc(#{$spacing-md} + 20px + #{$spacing-sm});
    border: 1px solid $border-color;
    border-radius: $border-radius-md;
    font-size: 1rem;
    background: $bg-primary;
    color: $text-primary;
    transition: all $transition-fast;

    &:focus {
      outline: none;
      border-color: $primary-color;
      box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.1);
    }

    &::placeholder {
      color: $text-secondary;
    }

    // Add padding for password toggle button
    &[type="password"],
    &[type="text"]:has(+ .login-form__toggle-password) {
      padding-right: calc(#{$spacing-md} + 20px + #{$spacing-sm});
    }
  }

  &__toggle-password {
    position: absolute;
    right: $spacing-md;
    background: none;
    border: none;
    color: $text-secondary;
    cursor: pointer;
    padding: $spacing-xs;
    border-radius: $border-radius-sm;
    transition: all $transition-fast;

    &:hover {
      color: $text-primary;
      background: $bg-secondary;
    }

    &:focus {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
    }
  }

  &__submit {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-sm;
    padding: $spacing-md;
    background: $primary-color;
    color: $text-light;
    border: none;
    border-radius: $border-radius-md;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all $transition-fast;
    min-height: 48px;

    &:hover:not(:disabled) {
      background: $primary-hover;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    &:focus {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
    }
  }

  &__demo {
    text-align: center;
    padding-top: $spacing-md;
    border-top: 1px solid $border-color;
  }

  &__demo-text {
    margin: 0 0 $spacing-sm 0;
    font-size: 0.75rem;
    color: $text-secondary;
    font-weight: 500;
  }

  &__demo-button {
    background: none;
    border: 1px solid $border-color;
    color: $text-secondary;
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-sm;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all $transition-fast;

    &:hover {
      background: $bg-secondary;
      color: $text-primary;
      border-color: $text-secondary;
    }

    &:focus {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
    }
  }

  &__footer {
    text-align: center;
    margin-top: $spacing-xl;
    padding-top: $spacing-md;
    border-top: 1px solid $border-color;

    p {
      margin: 0;
      font-size: 0.75rem;
      color: $text-secondary;
    }
  }

  // Mobile optimizations
  @media (max-width: $breakpoint-sm) {
    padding: $spacing-sm;

    &__container {
      margin: 0;
    }

    &__input {
      font-size: 16px; // Prevent zoom on iOS
    }
  }

  // Touch targets for mobile
  @media (max-width: $breakpoint-md) {
    &__submit {
      min-height: 44px;
      padding: $spacing-lg;
    }

    &__toggle-password {
      min-width: 44px;
      min-height: 44px;
    }
  }
}
