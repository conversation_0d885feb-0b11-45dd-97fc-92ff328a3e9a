import React, { createContext, useReducer, useEffect } from 'react';
import * as <PERSON> from '@apollo/client';
import { GET_USER_INFO, LOGOUT_USER } from '../graphql/queries';

// eslint-disable-next-line react-refresh/only-export-components
export const AuthContext = createContext();

const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
        error: null,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      };
    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: action.payload,
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

const initialState = {
  user: null,
  token: localStorage.getItem('authToken'),
  isAuthenticated: false,
  loading: true,
  error: null,
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if we're in development mode and have a dev token
  const isDev = state.token && state.token.startsWith('dev_token_');

  const { loading: userLoading } = Apollo.useQuery(
    GET_USER_INFO,
    {
      skip: !state.token || isDev, // Skip GraphQL query for dev tokens
      onCompleted: (data) => {
        if (data?.user) {
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: {
              user: data.user,
              token: state.token,
            },
          });
        }
      },
      onError: (error) => {
        console.error('Auth error:', error);
        dispatch({
          type: 'AUTH_ERROR',
          payload: error.message,
        });
        localStorage.removeItem('authToken');
      },
    }
  );

  const [logoutMutation] = Apollo.useMutation(LOGOUT_USER, {
    onCompleted: () => {
      localStorage.removeItem('authToken');
      dispatch({ type: 'LOGOUT' });
      window.location.reload();
    },
    onError: (error) => {
      console.error('Logout error:', error);
      localStorage.removeItem('authToken');
      dispatch({ type: 'LOGOUT' });
    },
  });

  useEffect(() => {
    if (!state.token) {
      dispatch({ type: 'SET_LOADING', payload: false });
    } else if (isDev) {
      // For development tokens, restore user from localStorage or use default
      const storedUser = localStorage.getItem('devUser');
      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: {
              user,
              token: state.token,
            },
          });
        } catch (error) {
          console.error('Error parsing stored user:', error);
          localStorage.removeItem('authToken');
          localStorage.removeItem('devUser');
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } else {
        localStorage.removeItem('authToken');
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  }, [state.token, isDev]);

  const login = (token, user) => {
    localStorage.setItem('authToken', token);
    // Store user data for dev tokens
    if (token.startsWith('dev_token_')) {
      localStorage.setItem('devUser', JSON.stringify(user));
    }
    dispatch({
      type: 'LOGIN_SUCCESS',
      payload: { token, user },
    });
  };

  const logout = async () => {
    try {
      // For dev tokens, skip GraphQL mutation
      if (state.token && state.token.startsWith('dev_token_')) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('devUser');
        dispatch({ type: 'LOGOUT' });
      } else {
        await logoutMutation();
      }
    } catch (error) {
      console.error('Logout mutation failed:', error);
      localStorage.removeItem('authToken');
      localStorage.removeItem('devUser');
      dispatch({ type: 'LOGOUT' });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value = {
    ...state,
    login,
    logout,
    clearError,
    loading: state.loading || userLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};




