import React from 'react';
import { Home, Bar<PERSON>hart3, <PERSON>, Settings, X } from 'lucide-react';
import './Sidebar.scss';

const Sidebar = ({ isOpen, onClose, activeItem, onNavigate }) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <>
      {isOpen && <div className="sidebar__overlay" onClick={onClose} />}
      <aside className={`sidebar ${isOpen ? 'sidebar--open' : ''}`}>
        <div className="sidebar__header">
          <h2 className="sidebar__logo">Zalo</h2>
          <button
            className="sidebar__close-button"
            onClick={onClose}
            aria-label="Close sidebar"
          >
            <X size={20} />
          </button>
        </div>

        <nav className="sidebar__nav">
          <ul className="sidebar__menu">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <li key={item.id} className="sidebar__menu-item">
                  <button
                    className={`sidebar__menu-link ${
                      activeItem === item.id ? 'sidebar__menu-link--active' : ''
                    }`}
                    onClick={() => onNavigate(item.id)}
                  >
                    <Icon size={20} />
                    <span className="sidebar__menu-text">{item.label}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="sidebar__footer">
          <div className="sidebar__version">v1.0.0</div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;