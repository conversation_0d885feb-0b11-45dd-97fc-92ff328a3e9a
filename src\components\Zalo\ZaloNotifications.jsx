import React from 'react';
import { useQuery } from "@apollo/client/react";

import { Bell, Phone, AlertCircle, CheckCircle, Clock, RefreshCw } from 'lucide-react';
import { GET_ZALO_NOTIFICATIONS } from '../../graphql/queries';
import DashboardWidget from '../Dashboard/DashboardWidget';
import './ZaloNotifications.scss';

const ZaloNotifications = () => {
  const { data, loading, error, refetch } = useQuery(GET_ZALO_NOTIFICATIONS, {
    pollInterval: 10000, // Refresh every 10 seconds
    errorPolicy: 'all',
  });

  const notifications = data?.zaloNotifications || [];

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'success':
        return <CheckCircle className="notification-status__icon notification-status__icon--success" size={16} />;
      case 'failed':
      case 'error':
        return <AlertCircle className="notification-status__icon notification-status__icon--error" size={16} />;
      case 'pending':
        return <Clock className="notification-status__icon notification-status__icon--pending" size={16} />;
      default:
        return <Bell className="notification-status__icon notification-status__icon--default" size={16} />;
    }
  };

  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'success':
        return 'notification-status--success';
      case 'failed':
      case 'error':
        return 'notification-status--error';
      case 'pending':
        return 'notification-status--pending';
      default:
        return 'notification-status--default';
    }
  };

  const formatPhoneNumber = (phone) => {
    if (!phone) return 'N/A';
    // Format Vietnamese phone numbers
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
  };

  const formatDateTime = (dateTime) => {
    if (!dateTime) return 'N/A';
    try {
      const date = new Date(dateTime);
      return date.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return String(dateTime);
    }
  };

  return (
    <DashboardWidget
      title="Zalo Notifications"
      loading={loading}
      error={error?.message}
      onRetry={() => refetch()}
      className="zalo-notifications"
    >
      <div className="zalo-notifications__header">
        <div className="zalo-notifications__stats">
          <span className="zalo-notifications__count">
            Total: {notifications.length}
          </span>
          <button
            onClick={() => refetch()}
            className="zalo-notifications__refresh"
            title="Refresh notifications"
          >
            <RefreshCw size={16} />
          </button>
        </div>
      </div>

      <div className="zalo-notifications__list">
        {notifications.length > 0 ? (
          notifications.map((notification, index) => (
            <div key={index} className="notification-item">
              <div className="notification-item__header">
                <div className="notification-item__name">
                  <Bell size={16} />
                  <span>{notification.name || 'Unnamed Notification'}</span>
                </div>
                <div className={`notification-status ${getStatusClass(notification.status)}`}>
                  {getStatusIcon(notification.status)}
                  <span className="notification-status__text">
                    {notification.status || 'Unknown'}
                  </span>
                </div>
              </div>

              <div className="notification-item__details">
                <div className="notification-item__detail">
                  <span className="notification-item__label">Type:</span>
                  <span className="notification-item__value">
                    {notification.type || 'N/A'}
                  </span>
                </div>

                <div className="notification-item__detail">
                  <Phone size={14} />
                  <span className="notification-item__label">Phone:</span>
                  <span className="notification-item__value">
                    {formatPhoneNumber(notification.phoneNumber)}
                  </span>
                </div>

                {notification.error && (
                  <div className="notification-item__error">
                    <AlertCircle size={14} />
                    <span className="notification-item__error-text">
                      {notification.error}
                    </span>
                  </div>
                )}
              </div>

              <div className="notification-item__timestamps">
                <div className="notification-item__timestamp">
                  <span className="notification-item__label">Created:</span>
                  <span className="notification-item__value">
                    {formatDateTime(notification.createdTime)}
                  </span>
                </div>
                {notification.updatedTime && notification.updatedTime !== notification.createdTime && (
                  <div className="notification-item__timestamp">
                    <span className="notification-item__label">Updated:</span>
                    <span className="notification-item__value">
                      {formatDateTime(notification.updatedTime)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="zalo-notifications__empty">
            <Bell size={48} />
            <p>No notifications found</p>
          </div>
        )}
      </div>
    </DashboardWidget>
  );
};

export default ZaloNotifications;