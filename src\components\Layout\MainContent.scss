@use '../../styles/variables' as *;

.main-content {
  padding: $spacing-md;
  min-height: calc(100vh - #{$header-height});
  background: $bg-secondary;

  &__container {
    max-width: 100%;
    margin: 0 auto;
  }

  &__header {
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $border-color;
  }

  &__title {
    font-size: 1.5rem;
    font-weight: 600;
    color: $text-primary;
    margin: 0 0 $spacing-sm 0;

    @media (min-width: $breakpoint-md) {
      font-size: 1.75rem;
    }

    @media (min-width: $breakpoint-lg) {
      font-size: 2rem;
    }
  }

  &__subtitle {
    font-size: 1rem;
    color: $text-secondary;
    margin: 0;
    line-height: 1.5;
  }

  &__body {
    background: $bg-primary;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    box-shadow: 0 2px 8px $shadow-light;
    min-height: 400px;

    @media (min-width: $breakpoint-md) {
      padding: $spacing-xl;
    }
  }

  // Tablet and up
  @media (min-width: $breakpoint-md) {
    padding: $spacing-lg;

    &__container {
      max-width: 1200px;
    }
  }

  // Desktop and up
  @media (min-width: $breakpoint-lg) {
    padding: $spacing-xl;

    &__container {
      max-width: 1400px;
    }
  }
}