@use '../../styles/variables' as *;

.zalo-notifications {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $border-color;
  }

  &__stats {
    display: flex;
    align-items: center;
    gap: $spacing-md;
  }

  &__count {
    font-size: 0.875rem;
    color: $text-secondary;
    font-weight: 500;
  }

  &__refresh {
    background: none;
    border: 1px solid $border-color;
    padding: $spacing-xs;
    border-radius: $border-radius-sm;
    cursor: pointer;
    color: $text-secondary;
    transition: all $transition-fast;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: $bg-secondary;
      color: $text-primary;
      border-color: $text-secondary;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    max-height: 600px;
    overflow-y: auto;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: $bg-secondary;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: $text-secondary;
      border-radius: 3px;

      &:hover {
        background: $text-primary;
      }
    }
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    color: $text-secondary;
    text-align: center;

    svg {
      margin-bottom: $spacing-md;
      opacity: 0.5;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }
}

.notification-item {
  background: $bg-primary;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  transition: all $transition-fast;

  &:hover {
    border-color: $primary-color;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-md;

    @media (max-width: $breakpoint-sm) {
      flex-direction: column;
      gap: $spacing-sm;
      align-items: stretch;
    }
  }

  &__name {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    font-weight: 600;
    color: $text-primary;
    font-size: 0.875rem;
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
    margin-bottom: $spacing-md;
  }

  &__detail {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: 0.8rem;
  }

  &__label {
    color: $text-secondary;
    font-weight: 500;
    min-width: 60px;
  }

  &__value {
    color: $text-primary;
    flex: 1;
  }

  &__error {
    display: flex;
    align-items: flex-start;
    gap: $spacing-xs;
    background: rgba(255, 77, 79, 0.1);
    padding: $spacing-sm;
    border-radius: $border-radius-sm;
    border-left: 3px solid $error-color;
  }

  &__error-text {
    color: $error-color;
    font-size: 0.75rem;
    line-height: 1.4;
  }

  &__timestamps {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    font-size: 0.75rem;
    color: $text-secondary;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: $spacing-sm;

    @media (min-width: $breakpoint-sm) {
      flex-direction: row;
      justify-content: space-between;
    }
  }

  &__timestamp {
    display: flex;
    gap: $spacing-xs;
  }
}

.notification-status {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: 0.75rem;
  font-weight: 500;

  &--success {
    background: rgba(82, 196, 26, 0.1);
    color: $success-color;
    border: 1px solid rgba(82, 196, 26, 0.3);
  }

  &--error {
    background: rgba(255, 77, 79, 0.1);
    color: $error-color;
    border: 1px solid rgba(255, 77, 79, 0.3);
  }

  &--pending {
    background: rgba(250, 173, 20, 0.1);
    color: $warning-color;
    border: 1px solid rgba(250, 173, 20, 0.3);
  }

  &--default {
    background: rgba(140, 140, 140, 0.1);
    color: $text-secondary;
    border: 1px solid rgba(140, 140, 140, 0.3);
  }

  &__icon {
    flex-shrink: 0;

    &--success {
      color: $success-color;
    }

    &--error {
      color: $error-color;
    }

    &--pending {
      color: $warning-color;
    }

    &--default {
      color: $text-secondary;
    }
  }

  &__text {
    text-transform: capitalize;
  }
}

// Mobile responsive adjustments
@media (max-width: $breakpoint-sm) {
  .notification-item {
    padding: $spacing-sm;

    &__header {
      margin-bottom: $spacing-sm;
    }

    &__details {
      margin-bottom: $spacing-sm;
    }

    &__detail {
      font-size: 0.75rem;
    }

    &__timestamps {
      font-size: 0.7rem;
    }
  }

  .notification-status {
    align-self: flex-start;
  }
}