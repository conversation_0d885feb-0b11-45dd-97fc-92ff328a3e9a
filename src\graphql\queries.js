import { gql } from '@apollo/client';

export const GET_USER_INFO = gql`
  query GetUserInfo {
    user {
      id
      name
      email
      isAuthenticated
      role
      avatar
    }
  }
`;

export const GET_ZALO_NOTIFICATIONS = gql`
  query GetZaloNotifications {
    zaloNotifications {
      name
      type
      phoneNumber
      status
      error
      createdTime
      updatedTime
    }
  }
`;

export const GET_DASHBOARD_DATA = gql`
  query GetDashboardData {
    dashboardData {
      metrics {
        totalUsers
        activeUsers
        totalMessages
        growthRate
      }
      recentActivity {
        id
        type
        message
        timestamp
        user {
          name
          avatar
        }
      }
      analytics {
        userGrowth {
          date
          count
        }
        messageVolume {
          date
          count
        }
      }
    }
  }
`;

export const GET_USERS = gql`
  query GetUsers($limit: Int, $offset: Int, $search: String) {
    users(limit: $limit, offset: $offset, search: $search) {
      id
      name
      email
      status
      lastActive
      avatar
    }
  }
`;

export const LOGIN_USER = gql`
  mutation LoginUser($email: String!, $password: String!) {
    login(email: $email, password: $password) {
      token
      user {
        id
        name
        email
        role
      }
    }
  }
`;

export const LOGOUT_USER = gql`
  mutation LogoutUser {
    logout {
      success
    }
  }
`;