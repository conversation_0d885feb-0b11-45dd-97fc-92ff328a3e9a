import React from 'react';
import { useQuery } from '@apollo/client';
import { Users, MessageSquare, TrendingUp, Activity } from 'lucide-react';
import { GET_DASHBOARD_DATA } from '../../graphql/queries';
import DashboardWidget from './DashboardWidget';
import MainContent from '../Layout/MainContent';
import ZaloNotifications from '../Zalo/ZaloNotifications';
import './Dashboard.scss';

const Dashboard = () => {
  const { data, loading, error, refetch } = useQuery(GET_DASHBOARD_DATA, {
    pollInterval: 30000,
    errorPolicy: 'all',
  });

  const metrics = data?.dashboardData?.metrics || {};
  const recentActivity = data?.dashboardData?.recentActivity || [];

  return (
    <MainContent
      title="Dashboard"
      subtitle="Welcome to your Zalo Dashboard. Monitor your application metrics and activity."
    >
      <div className="dashboard">
        <div className="dashboard__metrics">
          <DashboardWidget
            title="Total Users"
            loading={loading}
            error={error?.message}
            onRetry={() => refetch()}
            className="dashboard__metric-card"
          >
            <div className="metric-card">
              <Users className="metric-card__icon" size={32} />
              <div className="metric-card__content">
                <div className="metric-card__value">
                  {metrics.totalUsers?.toLocaleString() || '0'}
                </div>
                <div className="metric-card__label">Total Users</div>
              </div>
            </div>
          </DashboardWidget>

          <DashboardWidget
            title="Active Users"
            loading={loading}
            error={error?.message}
            onRetry={() => refetch()}
            className="dashboard__metric-card"
          >
            <div className="metric-card">
              <Activity className="metric-card__icon metric-card__icon--success" size={32} />
              <div className="metric-card__content">
                <div className="metric-card__value">
                  {metrics.activeUsers?.toLocaleString() || '0'}
                </div>
                <div className="metric-card__label">Active Users</div>
              </div>
            </div>
          </DashboardWidget>

          <DashboardWidget
            title="Total Messages"
            loading={loading}
            error={error?.message}
            onRetry={() => refetch()}
            className="dashboard__metric-card"
          >
            <div className="metric-card">
              <MessageSquare className="metric-card__icon metric-card__icon--info" size={32} />
              <div className="metric-card__content">
                <div className="metric-card__value">
                  {metrics.totalMessages?.toLocaleString() || '0'}
                </div>
                <div className="metric-card__label">Messages</div>
              </div>
            </div>
          </DashboardWidget>

          <DashboardWidget
            title="Growth Rate"
            loading={loading}
            error={error?.message}
            onRetry={() => refetch()}
            className="dashboard__metric-card"
          >
            <div className="metric-card">
              <TrendingUp className="metric-card__icon metric-card__icon--warning" size={32} />
              <div className="metric-card__content">
                <div className="metric-card__value">
                  {metrics.growthRate ? `${metrics.growthRate}%` : '0%'}
                </div>
                <div className="metric-card__label">Growth Rate</div>
              </div>
            </div>
          </DashboardWidget>
        </div>

        <div className="dashboard__content">
          <div className="dashboard__activity">
            <DashboardWidget
              title="Recent Activity"
              loading={loading}
              error={error?.message}
              onRetry={() => refetch()}
              className="dashboard__activity-widget"
            >
              <div className="activity-list">
                {recentActivity.length > 0 ? (
                  recentActivity.map((activity) => (
                    <div key={activity.id} className="activity-item">
                      <div className="activity-item__avatar">
                        {activity.user?.avatar ? (
                          <img
                            src={activity.user.avatar}
                            alt={activity.user.name}
                            className="activity-item__avatar-img"
                          />
                        ) : (
                          <div className="activity-item__avatar-placeholder">
                            {activity.user?.name?.charAt(0) || '?'}
                          </div>
                        )}
                      </div>
                      <div className="activity-item__content">
                        <div className="activity-item__message">
                          {activity.message}
                        </div>
                        <div className="activity-item__meta">
                          <span className="activity-item__user">
                            {activity.user?.name || 'Unknown'}
                          </span>
                          <span className="activity-item__time">
                            {new Date(activity.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="activity-list__empty">
                    <Activity size={48} />
                    <p>No recent activity</p>
                  </div>
                )}
              </div>
            </DashboardWidget>
          </div>

          <div className="dashboard__zalo">
            <ZaloNotifications />
          </div>
        </div>
      </div>
    </MainContent>
  );
};

export default Dashboard;