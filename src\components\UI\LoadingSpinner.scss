@use '../../styles/variables' as *;

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $spacing-md;
  padding: $spacing-xl;

  &--small {
    padding: $spacing-md;
    gap: $spacing-sm;

    .loading-spinner__circle {
      width: 20px;
      height: 20px;
      border-width: 2px;
    }

    .loading-spinner__text {
      font-size: 0.75rem;
    }
  }

  &--medium {
    .loading-spinner__circle {
      width: 32px;
      height: 32px;
      border-width: 3px;
    }

    .loading-spinner__text {
      font-size: 0.875rem;
    }
  }

  &--large {
    padding: $spacing-xxl;
    gap: $spacing-lg;

    .loading-spinner__circle {
      width: 48px;
      height: 48px;
      border-width: 4px;
    }

    .loading-spinner__text {
      font-size: 1rem;
    }
  }

  &__circle {
    border: 3px solid $border-color;
    border-top: 3px solid $primary-color;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    animation: spin 1s linear infinite;
  }

  &__text {
    margin: 0;
    color: $text-secondary;
    font-weight: 500;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}